<?php

use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PublicController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Public Pages
Route::get('/', [PublicController::class, 'home'])->name('home');
Route::get('/about', [PublicController::class, 'about'])->name('about');
Route::get('/services', [PublicController::class, 'services'])->name('services');
Route::get('/services/{slug}', [PublicController::class, 'serviceDetail'])->name('service.show');
Route::get('/team', [PublicController::class, 'team'])->name('team');
Route::get('/blog', [PublicController::class, 'blog'])->name('blog');
Route::get('/blog/{slug}', [PublicController::class, 'blogPost'])->name('blog.show');
Route::get('/contact', [PublicController::class, 'contact'])->name('contact');
Route::get('/book-consultation', [PublicController::class, 'bookConsultation'])->name('consultation.book');

// Legacy welcome route for compatibility
Route::get('/welcome', function () {
    return Inertia::render('welcome');
})->name('welcome');

// Protected Pages
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
});

// Admin Pages (admin role required)
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('dashboard', [AdminDashboardController::class, 'index'])->name('dashboard.index');

    // User Management
    Route::get('users', [AdminDashboardController::class, 'users'])->name('users');
    Route::get('users/{user}', [AdminDashboardController::class, 'userShow'])->name('users.show');

    // Service Management
    Route::get('services', [AdminDashboardController::class, 'services'])->name('services');
    Route::get('services/{service}', [AdminDashboardController::class, 'serviceShow'])->name('services.show');

    // Blog Management
    Route::get('blog', [AdminDashboardController::class, 'blog'])->name('blog');
    Route::get('blog/posts', [AdminDashboardController::class, 'blogPosts'])->name('blog.posts');
    Route::get('blog/posts/{post}', [AdminDashboardController::class, 'blogPostShow'])->name('blog.posts.show');
    Route::get('blog/categories', [AdminDashboardController::class, 'blogCategories'])->name('blog.categories');

    // Team Management
    Route::get('team', [AdminDashboardController::class, 'team'])->name('team');
    Route::get('team/{member}', [AdminDashboardController::class, 'teamShow'])->name('team.show');

    // Consultation Management
    Route::get('consultations', [AdminDashboardController::class, 'consultations'])->name('consultations');
    Route::get('consultations/{consultation}', [AdminDashboardController::class, 'consultationShow'])->name('consultations.show');

    // Payment Management
    Route::get('payments', [AdminDashboardController::class, 'payments'])->name('payments');
    Route::get('payments/{payment}', [AdminDashboardController::class, 'paymentShow'])->name('payments.show');

    // Analytics
    Route::get('analytics', [AdminDashboardController::class, 'analytics'])->name('analytics');

    // Contact & Newsletter Management
    Route::get('contacts', [AdminDashboardController::class, 'contacts'])->name('contacts');
    Route::get('newsletter', [AdminDashboardController::class, 'newsletter'])->name('newsletter');

    // Settings
    Route::get('settings', [AdminDashboardController::class, 'settings'])->name('settings');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
