{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "types": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:jest": "jest", "test:jest:watch": "jest --watch", "test:jest:coverage": "jest --coverage", "test:frontend": "jest", "test:backend": "php artisan test", "test:all": "npm run test:backend && npm run test:frontend"}, "devDependencies": {"@eslint/js": "^9.19.0", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.13.5", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "ts-jest": "^29.4.1", "typescript-eslint": "^8.23.0", "vitest": "^3.2.4"}, "dependencies": {"@headlessui/react": "^2.2.0", "@inertiajs/react": "^2.1.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/vite": "^4.1.11", "@types/react": "^19.0.3", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "globals": "^15.14.0", "laravel-vite-plugin": "^2.0", "lucide-react": "^0.475.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "vite": "^7.0.4"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}