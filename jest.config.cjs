module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'jsdom',
    setupFilesAfterEnv: ['<rootDir>/resources/js/__tests__/setup.ts'],
    moduleNameMapping: {
        '^@/(.*)$': '<rootDir>/resources/js/$1',
    },
    testMatch: [
        '<rootDir>/resources/js/__tests__/**/*.test.{ts,tsx}',
    ],
    collectCoverageFrom: [
        'resources/js/**/*.{ts,tsx}',
        '!resources/js/**/*.d.ts',
        '!resources/js/__tests__/**/*',
        '!resources/js/types/**/*',
    ],
    coverageDirectory: 'coverage',
    coverageReporters: ['text', 'lcov', 'html'],
    transform: {
        '^.+\\.(ts|tsx)$': 'ts-jest',
    },
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
    transformIgnorePatterns: [
        'node_modules/(?!(.*\\.mjs$))',
    ],
    globals: {
        'ts-jest': {
            tsconfig: {
                jsx: 'react-jsx',
            },
        },
    },
};
