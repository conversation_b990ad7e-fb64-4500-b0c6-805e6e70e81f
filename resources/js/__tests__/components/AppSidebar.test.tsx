import { render, screen } from '@testing-library/react';
import { AppSidebar } from '@/components/app-sidebar';
import { type User } from '@/types';

// Mock Inertia usePage hook
const mockUsePage = jest.fn();
jest.mock('@inertiajs/react', () => ({
    usePage: () => mockUsePage(),
    Link: ({ children, href, ...props }: any) => (
        <a href={href} {...props}>
            {children}
        </a>
    ),
}));

// Mock sidebar components
jest.mock('@/components/ui/sidebar', () => ({
    Sidebar: ({ children }: any) => <div data-testid="sidebar">{children}</div>,
    SidebarContent: ({ children }: any) => <div data-testid="sidebar-content">{children}</div>,
    SidebarFooter: ({ children }: any) => <div data-testid="sidebar-footer">{children}</div>,
    SidebarHeader: ({ children }: any) => <div data-testid="sidebar-header">{children}</div>,
    SidebarMenu: ({ children }: any) => <div data-testid="sidebar-menu">{children}</div>,
    SidebarMenuButton: ({ children }: any) => <div data-testid="sidebar-menu-button">{children}</div>,
    SidebarMenuItem: ({ children }: any) => <div data-testid="sidebar-menu-item">{children}</div>,
}));

jest.mock('@/components/nav-main', () => ({
    NavMain: ({ items }: any) => (
        <div data-testid="nav-main">
            {items.map((item: any, index: number) => (
                <div key={index} data-testid={`nav-item-${item.title.toLowerCase().replace(/\s+/g, '-')}`}>
                    {item.title}
                </div>
            ))}
        </div>
    ),
}));

jest.mock('@/components/nav-footer', () => ({
    NavFooter: ({ items }: any) => (
        <div data-testid="nav-footer">
            {items.map((item: any, index: number) => (
                <div key={index} data-testid={`footer-item-${item.title.toLowerCase().replace(/\s+/g, '-')}`}>
                    {item.title}
                </div>
            ))}
        </div>
    ),
}));

jest.mock('@/components/nav-user', () => ({
    NavUser: () => <div data-testid="nav-user">User Nav</div>,
}));

jest.mock('@/components/app-logo', () => {
    return function AppLogo() {
        return <div data-testid="app-logo">Logo</div>;
    };
});

const mockAdminUser: User = {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
};

const mockClientUser: User = {
    id: '2',
    name: 'Client User',
    email: '<EMAIL>',
    role: 'client',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
};

const mockTeamMemberUser: User = {
    id: '3',
    name: 'Team Member',
    email: '<EMAIL>',
    role: 'team_member',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
};

describe('AppSidebar', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders sidebar structure correctly', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(<AppSidebar />);

        expect(screen.getByTestId('sidebar')).toBeInTheDocument();
        expect(screen.getByTestId('sidebar-header')).toBeInTheDocument();
        expect(screen.getByTestId('sidebar-content')).toBeInTheDocument();
        expect(screen.getByTestId('sidebar-footer')).toBeInTheDocument();
        expect(screen.getByTestId('app-logo')).toBeInTheDocument();
        expect(screen.getByTestId('nav-user')).toBeInTheDocument();
    });

    it('shows admin navigation for admin users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockAdminUser }
            }
        });

        render(<AppSidebar />);

        expect(screen.getByTestId('nav-item-dashboard')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-admin-panel')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-users')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-services')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-analytics')).toBeInTheDocument();

        expect(screen.getByText('Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Admin Panel')).toBeInTheDocument();
        expect(screen.getByText('Users')).toBeInTheDocument();
        expect(screen.getByText('Services')).toBeInTheDocument();
        expect(screen.getByText('Analytics')).toBeInTheDocument();
    });

    it('shows client navigation for client users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(<AppSidebar />);

        expect(screen.getByTestId('nav-item-dashboard')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-book-consultation')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-services')).toBeInTheDocument();

        expect(screen.getByText('Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Book Consultation')).toBeInTheDocument();
        expect(screen.getByText('Services')).toBeInTheDocument();

        // Should not show admin items
        expect(screen.queryByTestId('nav-item-admin-panel')).not.toBeInTheDocument();
        expect(screen.queryByTestId('nav-item-users')).not.toBeInTheDocument();
        expect(screen.queryByTestId('nav-item-analytics')).not.toBeInTheDocument();
    });

    it('shows team member navigation for team member users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockTeamMemberUser }
            }
        });

        render(<AppSidebar />);

        expect(screen.getByTestId('nav-item-dashboard')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-consultations')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-team')).toBeInTheDocument();

        expect(screen.getByText('Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Consultations')).toBeInTheDocument();
        expect(screen.getByText('Team')).toBeInTheDocument();

        // Should not show admin items
        expect(screen.queryByTestId('nav-item-admin-panel')).not.toBeInTheDocument();
        expect(screen.queryByTestId('nav-item-users')).not.toBeInTheDocument();
        expect(screen.queryByTestId('nav-item-analytics')).not.toBeInTheDocument();

        // Should not show client-specific items
        expect(screen.queryByTestId('nav-item-book-consultation')).not.toBeInTheDocument();
    });

    it('shows admin footer items for admin users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockAdminUser }
            }
        });

        render(<AppSidebar />);

        expect(screen.getByTestId('footer-item-public-site')).toBeInTheDocument();
        expect(screen.getByTestId('footer-item-settings')).toBeInTheDocument();

        expect(screen.getByText('Public Site')).toBeInTheDocument();
        expect(screen.getByText('Settings')).toBeInTheDocument();
    });

    it('shows client footer items for client users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(<AppSidebar />);

        expect(screen.getByTestId('footer-item-help')).toBeInTheDocument();
        expect(screen.getByTestId('footer-item-settings')).toBeInTheDocument();

        expect(screen.getByText('Help')).toBeInTheDocument();
        expect(screen.getByText('Settings')).toBeInTheDocument();

        // Should not show admin footer items
        expect(screen.queryByTestId('footer-item-public-site')).not.toBeInTheDocument();
    });

    it('shows team member footer items for team member users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockTeamMemberUser }
            }
        });

        render(<AppSidebar />);

        expect(screen.getByTestId('footer-item-help')).toBeInTheDocument();
        expect(screen.getByTestId('footer-item-settings')).toBeInTheDocument();

        expect(screen.getByText('Help')).toBeInTheDocument();
        expect(screen.getByText('Settings')).toBeInTheDocument();
    });

    it('handles missing user gracefully', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: null }
            }
        });

        // Should not crash when user is null
        expect(() => render(<AppSidebar />)).not.toThrow();
    });

    it('handles invalid user role gracefully', () => {
        const invalidUser = { ...mockClientUser, role: 'invalid_role' as any };
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: invalidUser }
            }
        });

        render(<AppSidebar />);

        // Should default to client navigation
        expect(screen.getByTestId('nav-item-dashboard')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-book-consultation')).toBeInTheDocument();
        expect(screen.getByTestId('nav-item-services')).toBeInTheDocument();
    });

    it('renders logo with correct link', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(<AppSidebar />);

        const logoLink = screen.getByTestId('app-logo').closest('a');
        expect(logoLink).toHaveAttribute('href', '/dashboard');
    });

    it('includes all required navigation components', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockAdminUser }
            }
        });

        render(<AppSidebar />);

        expect(screen.getByTestId('nav-main')).toBeInTheDocument();
        expect(screen.getByTestId('nav-footer')).toBeInTheDocument();
        expect(screen.getByTestId('nav-user')).toBeInTheDocument();
    });
});
