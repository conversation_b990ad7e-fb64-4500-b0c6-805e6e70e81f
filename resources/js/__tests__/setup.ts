import '@testing-library/jest-dom';

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
};

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
    writable: true,
    value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
});

// Suppress console warnings during tests
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
    if (
        typeof args[0] === 'string' &&
        args[0].includes('Warning: ReactDOM.render is deprecated')
    ) {
        return;
    }
    originalConsoleWarn.call(console, ...args);
};

// Mock route function for Inertia
global.route = jest.fn((name: string, params?: any) => {
    const routes: Record<string, string> = {
        'profile.update': '/settings/profile',
        'verification.send': '/email/verification-notification',
        'admin.dashboard': '/admin/dashboard',
        'admin.users': '/admin/users',
        'admin.services': '/admin/services',
        'dashboard': '/dashboard',
        'login': '/login',
    };
    
    let url = routes[name] || `/${name}`;
    
    if (params && typeof params === 'object') {
        Object.keys(params).forEach(key => {
            url = url.replace(`{${key}}`, params[key]);
        });
    }
    
    return url;
});

// Mock Intl.NumberFormat for consistent currency formatting in tests
const mockNumberFormat = {
    format: jest.fn((number: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(number);
    }),
};

Object.defineProperty(Intl, 'NumberFormat', {
    writable: true,
    value: jest.fn(() => mockNumberFormat),
});

// Mock Date.toLocaleDateString for consistent date formatting
const originalToLocaleDateString = Date.prototype.toLocaleDateString;
Date.prototype.toLocaleDateString = function(locale?: string, options?: Intl.DateTimeFormatOptions) {
    // Return consistent format for tests
    if (options?.month === 'short') {
        return originalToLocaleDateString.call(this, 'en-US', options);
    }
    return originalToLocaleDateString.call(this, locale, options);
};

// Mock Date.toLocaleString for consistent datetime formatting
const originalToLocaleString = Date.prototype.toLocaleString;
Date.prototype.toLocaleString = function(locale?: string, options?: Intl.DateTimeFormatOptions) {
    return originalToLocaleString.call(this, 'en-US', options);
};
