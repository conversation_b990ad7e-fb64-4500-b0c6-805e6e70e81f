import AdminLayout from '@/layouts/admin-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { type BreadcrumbItem } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { 
    BarChart3, 
    TrendingUp, 
    <PERSON>, 
    Eye,
    MousePointer,
    DollarSign,
    Calendar,
    RefreshCw
} from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Analytics',
        href: '/admin/analytics',
    },
];

interface AnalyticsData {
    overview: {
        total_events: number;
        unique_users: number;
        total_conversions: number;
        conversion_value: number;
    };
    events: Array<{
        event_name: string;
        count: number;
        percentage: number;
    }>;
    conversions: Array<{
        date: string;
        conversions: number;
        value: number;
    }>;
    traffic_sources: Array<{
        source: string;
        users: number;
        percentage: number;
    }>;
    popular_pages: Array<{
        page_url: string;
        views: number;
        unique_views: number;
    }>;
}

interface AnalyticsProps {
    analyticsData: AnalyticsData;
    filters: {
        date_from: string;
        date_to: string;
    };
}

export default function Analytics() {
    const { props } = usePage<AnalyticsProps>();
    const { analyticsData, filters } = props;
    
    const [dateFrom, setDateFrom] = useState(filters.date_from);
    const [dateTo, setDateTo] = useState(filters.date_to);

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const formatNumber = (num: number) => {
        return new Intl.NumberFormat('en-US').format(num);
    };

    const handleFilterUpdate = () => {
        router.get('/admin/analytics', {
            date_from: dateFrom,
            date_to: dateTo,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Analytics Dashboard" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6 overflow-x-auto">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
                        <p className="text-muted-foreground">
                            Track your website performance and user behavior.
                        </p>
                    </div>
                    
                    <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                            <Label htmlFor="date_from">From:</Label>
                            <Input
                                id="date_from"
                                type="date"
                                value={dateFrom}
                                onChange={(e) => setDateFrom(e.target.value)}
                                className="w-auto"
                            />
                        </div>
                        <div className="flex items-center gap-2">
                            <Label htmlFor="date_to">To:</Label>
                            <Input
                                id="date_to"
                                type="date"
                                value={dateTo}
                                onChange={(e) => setDateTo(e.target.value)}
                                className="w-auto"
                            />
                        </div>
                        <Button onClick={handleFilterUpdate} size="sm">
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Update
                        </Button>
                    </div>
                </div>

                {/* Overview Stats */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
                            <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(analyticsData.overview.total_events)}</div>
                            <p className="text-xs text-muted-foreground">
                                All tracked events
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Unique Users</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(analyticsData.overview.unique_users)}</div>
                            <p className="text-xs text-muted-foreground">
                                Distinct visitors
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Conversions</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(analyticsData.overview.total_conversions)}</div>
                            <p className="text-xs text-muted-foreground">
                                Goal completions
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Conversion Value</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(analyticsData.overview.conversion_value)}</div>
                            <p className="text-xs text-muted-foreground">
                                Total value generated
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Charts and Data */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Top Events</CardTitle>
                            <CardDescription>Most tracked user interactions</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {analyticsData.events.length > 0 ? (
                                    analyticsData.events.map((event, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="space-y-1">
                                                <p className="text-sm font-medium">{event.event_name}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {formatNumber(event.count)} events
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium">{event.percentage.toFixed(1)}%</p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-sm text-muted-foreground">No event data available</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Traffic Sources</CardTitle>
                            <CardDescription>Where your visitors come from</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {analyticsData.traffic_sources.length > 0 ? (
                                    analyticsData.traffic_sources.map((source, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="space-y-1">
                                                <p className="text-sm font-medium">{source.source}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {formatNumber(source.users)} users
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium">{source.percentage.toFixed(1)}%</p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-sm text-muted-foreground">No traffic source data available</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Popular Pages */}
                <Card>
                    <CardHeader>
                        <CardTitle>Popular Pages</CardTitle>
                        <CardDescription>Most visited pages on your website</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {analyticsData.popular_pages.length > 0 ? (
                                analyticsData.popular_pages.map((page, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="space-y-1 flex-1">
                                            <p className="text-sm font-medium truncate">{page.page_url}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {formatNumber(page.views)} views • {formatNumber(page.unique_views)} unique
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-4 text-sm">
                                            <div className="flex items-center gap-1">
                                                <Eye className="h-3 w-3" />
                                                {formatNumber(page.views)}
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <MousePointer className="h-3 w-3" />
                                                {formatNumber(page.unique_views)}
                                            </div>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-sm text-muted-foreground">No page data available</p>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Conversion Timeline */}
                <Card>
                    <CardHeader>
                        <CardTitle>Conversion Timeline</CardTitle>
                        <CardDescription>Daily conversion performance</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {analyticsData.conversions.length > 0 ? (
                                analyticsData.conversions.map((conversion, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium">
                                                {new Date(conversion.date).toLocaleDateString('en-US', {
                                                    month: 'short',
                                                    day: 'numeric',
                                                    year: 'numeric',
                                                })}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                {formatNumber(conversion.conversions)} conversions
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium">{formatCurrency(conversion.value)}</p>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-sm text-muted-foreground">No conversion data available</p>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
