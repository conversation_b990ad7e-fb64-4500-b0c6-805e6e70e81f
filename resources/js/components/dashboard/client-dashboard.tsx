import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type User, type Consultation, type Payment } from '@/types';
import { Link } from '@inertiajs/react';
import { 
    Calendar, 
    DollarSign, 
    Clock, 
    CheckCircle,
    CreditCard,
    BookOpen,
    User as UserIcon,
    Phone
} from 'lucide-react';

interface ClientDashboardProps {
    user: User;
    stats: Record<string, number>;
    upcomingConsultations: Consultation[];
    recentConsultations: Consultation[];
    recentPayments: Payment[];
}

export function ClientDashboard({ 
    user, 
    stats, 
    upcomingConsultations, 
    recentConsultations, 
    recentPayments 
}: ClientDashboardProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
        });
    };

    const formatDateTime = (dateString: string) => {
        return new Date(dateString).toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            pending: 'outline',
            confirmed: 'default',
            completed: 'secondary',
            cancelled: 'destructive',
            paid: 'secondary',
            failed: 'destructive',
        };
        return <Badge variant={variants[status] || 'outline'}>{status}</Badge>;
    };

    return (
        <div className="space-y-6">
            {/* Stats Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Consultations</CardTitle>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.total_consultations}</div>
                        <p className="text-xs text-muted-foreground">
                            {stats.completed_consultations} completed
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.upcoming_consultations}</div>
                        <p className="text-xs text-muted-foreground">
                            Scheduled consultations
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatCurrency(stats.total_spent)}</div>
                        <p className="text-xs text-muted-foreground">
                            Across all services
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.pending_payments}</div>
                        <p className="text-xs text-muted-foreground">
                            Require attention
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Upcoming Consultations */}
            {upcomingConsultations.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle>Upcoming Consultations</CardTitle>
                        <CardDescription>Your scheduled appointments</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {upcomingConsultations.map((consultation) => (
                                <div key={consultation.id} className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">
                                            {consultation.service?.title}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            {formatDateTime(consultation.consultation_date)} • {consultation.duration} minutes
                                        </p>
                                        {consultation.meeting_link && (
                                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                                <a href={consultation.meeting_link} target="_blank" rel="noopener noreferrer">
                                                    <Phone className="mr-1 h-3 w-3" />
                                                    Join Meeting
                                                </a>
                                            </Button>
                                        )}
                                    </div>
                                    {getStatusBadge(consultation.status)}
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Recent Activity */}
            <div className="grid gap-6 md:grid-cols-2">
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Consultations</CardTitle>
                        <CardDescription>Your consultation history</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {recentConsultations.length > 0 ? (
                                recentConsultations.map((consultation) => (
                                    <div key={consultation.id} className="flex items-center justify-between">
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium">
                                                {consultation.service?.title}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                {formatDate(consultation.consultation_date)} • {consultation.duration} min
                                            </p>
                                        </div>
                                        {getStatusBadge(consultation.status)}
                                    </div>
                                ))
                            ) : (
                                <p className="text-sm text-muted-foreground">No consultations yet</p>
                            )}
                        </div>
                        <div className="mt-4">
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/book-consultation">Book New Consultation</Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Payment History</CardTitle>
                        <CardDescription>Your recent transactions</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {recentPayments.length > 0 ? (
                                recentPayments.map((payment) => (
                                    <div key={payment.id} className="flex items-center justify-between">
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium">
                                                {formatCurrency(payment.amount)}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                {payment.consultation?.service?.title} • {formatDate(payment.created_at)}
                                            </p>
                                        </div>
                                        {getStatusBadge(payment.status)}
                                    </div>
                                ))
                            ) : (
                                <p className="text-sm text-muted-foreground">No payments yet</p>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                    <CardDescription>Common tasks and resources</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Button variant="outline" asChild>
                            <Link href="/book-consultation">
                                <Calendar className="mr-2 h-4 w-4" />
                                Book Consultation
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/services">
                                <BookOpen className="mr-2 h-4 w-4" />
                                View Services
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/settings/profile">
                                <UserIcon className="mr-2 h-4 w-4" />
                                Update Profile
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/contact">
                                <Phone className="mr-2 h-4 w-4" />
                                Contact Support
                            </Link>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
