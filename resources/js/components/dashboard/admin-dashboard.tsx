import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type Consultation, type Payment } from '@/types';
import { Link } from '@inertiajs/react';
import { 
    Users, 
    Calendar, 
    DollarSign, 
    TrendingUp, 
    Clock, 
    CheckCircle,
    UserCheck,
    Building
} from 'lucide-react';

interface AdminDashboardProps {
    stats: Record<string, number>;
    recentConsultations: Consultation[];
    recentPayments: Payment[];
}

export function AdminDashboard({ stats, recentConsultations, recentPayments }: AdminDashboardProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
        });
    };

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            pending: 'outline',
            confirmed: 'default',
            completed: 'secondary',
            cancelled: 'destructive',
        };
        return <Badge variant={variants[status] || 'outline'}>{status}</Badge>;
    };

    return (
        <div className="space-y-6">
            {/* Stats Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.total_users}</div>
                        <p className="text-xs text-muted-foreground">
                            {stats.total_clients} clients, {stats.total_team_members} team members
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Consultations</CardTitle>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.total_consultations}</div>
                        <p className="text-xs text-muted-foreground">
                            {stats.pending_consultations} pending, {stats.completed_consultations} completed
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatCurrency(stats.monthly_revenue)}</div>
                        <p className="text-xs text-muted-foreground">
                            Total: {formatCurrency(stats.total_revenue)}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Growth</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">+12.5%</div>
                        <p className="text-xs text-muted-foreground">
                            From last month
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Recent Activity */}
            <div className="grid gap-6 md:grid-cols-2">
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Consultations</CardTitle>
                        <CardDescription>Latest consultation bookings</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {recentConsultations.length > 0 ? (
                                recentConsultations.map((consultation) => (
                                    <div key={consultation.id} className="flex items-center justify-between">
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium">
                                                {consultation.user?.name || 'Unknown User'}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                {consultation.service?.title} • {formatDate(consultation.consultation_date)}
                                            </p>
                                        </div>
                                        {getStatusBadge(consultation.status)}
                                    </div>
                                ))
                            ) : (
                                <p className="text-sm text-muted-foreground">No recent consultations</p>
                            )}
                        </div>
                        <div className="mt-4">
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/admin/consultations">View All</Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Recent Payments</CardTitle>
                        <CardDescription>Latest payment transactions</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {recentPayments.length > 0 ? (
                                recentPayments.map((payment) => (
                                    <div key={payment.id} className="flex items-center justify-between">
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium">
                                                {payment.user?.name || 'Unknown User'}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                {formatCurrency(payment.amount)} • {formatDate(payment.created_at)}
                                            </p>
                                        </div>
                                        {getStatusBadge(payment.status)}
                                    </div>
                                ))
                            ) : (
                                <p className="text-sm text-muted-foreground">No recent payments</p>
                            )}
                        </div>
                        <div className="mt-4">
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/admin/payments">View All</Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                    <CardDescription>Common administrative tasks</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Button variant="outline" asChild>
                            <Link href="/admin/users">
                                <UserCheck className="mr-2 h-4 w-4" />
                                Manage Users
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/admin/services">
                                <Building className="mr-2 h-4 w-4" />
                                Manage Services
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/admin/blog">
                                <Clock className="mr-2 h-4 w-4" />
                                Manage Blog
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/admin/analytics">
                                <TrendingUp className="mr-2 h-4 w-4" />
                                View Analytics
                            </Link>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
