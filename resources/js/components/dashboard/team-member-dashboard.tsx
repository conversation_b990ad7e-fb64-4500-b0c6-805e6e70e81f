import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type User, type Consultation } from '@/types';
import { Link } from '@inertiajs/react';
import { 
    Calendar, 
    Clock, 
    CheckCircle,
    Users,
    TrendingUp,
    Phone,
    User as UserIcon,
    Settings
} from 'lucide-react';

interface TeamMemberDashboardProps {
    user: User;
    stats: Record<string, number>;
    assignedConsultations: Consultation[];
}

export function TeamMemberDashboard({ 
    user, 
    stats, 
    assignedConsultations 
}: TeamMemberDashboardProps) {
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
        });
    };

    const formatDateTime = (dateString: string) => {
        return new Date(dateString).toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            pending: 'outline',
            confirmed: 'default',
            completed: 'secondary',
            cancelled: 'destructive',
        };
        return <Badge variant={variants[status] || 'outline'}>{status}</Badge>;
    };

    const upcomingConsultations = assignedConsultations.filter(
        consultation => consultation.status === 'confirmed' && new Date(consultation.consultation_date) > new Date()
    );

    const todayConsultations = assignedConsultations.filter(
        consultation => {
            const consultationDate = new Date(consultation.consultation_date);
            const today = new Date();
            return consultationDate.toDateString() === today.toDateString();
        }
    );

    return (
        <div className="space-y-6">
            {/* Stats Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Assigned Consultations</CardTitle>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.assigned_consultations}</div>
                        <p className="text-xs text-muted-foreground">
                            Active assignments
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Completed This Month</CardTitle>
                        <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.completed_this_month}</div>
                        <p className="text-xs text-muted-foreground">
                            Consultations completed
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Today's Schedule</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{todayConsultations.length}</div>
                        <p className="text-xs text-muted-foreground">
                            Consultations today
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Performance</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">98%</div>
                        <p className="text-xs text-muted-foreground">
                            Completion rate
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Today's Consultations */}
            {todayConsultations.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle>Today's Schedule</CardTitle>
                        <CardDescription>Your consultations for today</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {todayConsultations.map((consultation) => (
                                <div key={consultation.id} className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">
                                            {consultation.service?.title}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            Client: {consultation.user?.name}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            {formatDateTime(consultation.consultation_date)} • {consultation.duration} minutes
                                        </p>
                                        {consultation.meeting_link && (
                                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                                <a href={consultation.meeting_link} target="_blank" rel="noopener noreferrer">
                                                    <Phone className="mr-1 h-3 w-3" />
                                                    Join Meeting
                                                </a>
                                            </Button>
                                        )}
                                    </div>
                                    {getStatusBadge(consultation.status)}
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Upcoming Consultations */}
            <Card>
                <CardHeader>
                    <CardTitle>Upcoming Consultations</CardTitle>
                    <CardDescription>Your scheduled appointments</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {upcomingConsultations.length > 0 ? (
                            upcomingConsultations.slice(0, 5).map((consultation) => (
                                <div key={consultation.id} className="flex items-center justify-between">
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">
                                            {consultation.service?.title}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            {consultation.user?.name} • {formatDateTime(consultation.consultation_date)}
                                        </p>
                                    </div>
                                    {getStatusBadge(consultation.status)}
                                </div>
                            ))
                        ) : (
                            <p className="text-sm text-muted-foreground">No upcoming consultations</p>
                        )}
                    </div>
                    {upcomingConsultations.length > 5 && (
                        <div className="mt-4">
                            <Button variant="outline" size="sm">
                                View All ({upcomingConsultations.length})
                            </Button>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
                <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                    <CardDescription>Your recent consultation history</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {assignedConsultations
                            .filter(consultation => consultation.status === 'completed')
                            .slice(0, 5)
                            .map((consultation) => (
                                <div key={consultation.id} className="flex items-center justify-between">
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">
                                            {consultation.service?.title}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            {consultation.user?.name} • {formatDate(consultation.consultation_date)}
                                        </p>
                                    </div>
                                    {getStatusBadge(consultation.status)}
                                </div>
                            ))}
                        {assignedConsultations.filter(c => c.status === 'completed').length === 0 && (
                            <p className="text-sm text-muted-foreground">No completed consultations yet</p>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                    <CardDescription>Common tasks and tools</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Button variant="outline" asChild>
                            <Link href="/team">
                                <Users className="mr-2 h-4 w-4" />
                                View Team
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/services">
                                <Calendar className="mr-2 h-4 w-4" />
                                View Services
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/settings/profile">
                                <UserIcon className="mr-2 h-4 w-4" />
                                Update Profile
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/contact">
                                <Settings className="mr-2 h-4 w-4" />
                                Support
                            </Link>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
